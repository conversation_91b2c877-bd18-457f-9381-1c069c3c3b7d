from django.contrib import admin
from django.utils.html import format_html
from django.utils import timezone
from utils.admin import BaseAdmin
from .models import (
    PropHuntGame, PropHuntParticipant, PropHuntSeason,
    BattlePassTier, BattlePassReward, BattlePassProgress, ClaimedReward,
    ProphauntWeapon, ProphauntWeaponOwns, ProphauntItemData
)


@admin.register(PropHuntGame)
class PropHuntGameAdmin(BaseAdmin):
    list_display = (
        'id', 'server_id', 'status', 'participant_count', 'winning_team',
        'start_time', 'duration_display', 'created'
    )
    list_filter = ('status', 'winning_team', 'start_time', 'created')
    search_fields = ('server_id', 'id')
    readonly_fields = ('duration_seconds', 'created', 'last_update')

    fieldsets = (
        ('Game Info', {
            'fields': ('server_id', 'status', 'winning_team')
        }),
        ('Timing', {
            'fields': ('start_time', 'end_time', 'duration_seconds')
        }),
        ('Settings', {
            'fields': ('max_players', 'round_duration')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def participant_count(self, obj):
        return obj.participants.count()
    participant_count.short_description = 'Players'

    def duration_display(self, obj):
        if obj.duration_seconds:
            minutes = obj.duration_seconds // 60
            seconds = obj.duration_seconds % 60
            return f"{minutes}m {seconds}s"
        return "-"
    duration_display.short_description = 'Duration'


@admin.register(PropHuntParticipant)
class PropHuntParticipantAdmin(BaseAdmin):
    list_display = (
        'member', 'game_id', 'server_id', 'team', 'kills', 'deaths', 'kd_ratio',
        'survived', 'coin_earned', 'smart_earned'
    )
    list_filter = ('team', 'survived', 'game__status', 'created')
    search_fields = ('member__username', 'member__handle', 'game__server_id', 'server_id')
    raw_id_fields = ('game', 'member')
    readonly_fields = ('created', 'last_update')

    fieldsets = (
        ('Player Info', {
            'fields': ('member', 'game', 'server_id', 'team')
        }),
        ('Statistics', {
            'fields': ('kills', 'deaths', 'survived')
        }),
        ('Rewards', {
            'fields': ('coin_earned', 'smart_earned')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def game_id(self, obj):
        return obj.game.id
    game_id.short_description = 'Game ID'

    def kd_ratio(self, obj):
        if obj.deaths == 0:
            return f"{obj.kills}.00" if obj.kills > 0 else "0.00"
        return f"{obj.kills / obj.deaths:.2f}"
    kd_ratio.short_description = 'K/D'


@admin.register(PropHuntSeason)
class PropHuntSeasonAdmin(BaseAdmin):
    list_display = (
        'name', 'is_active_display', 'start_date', 'end_date',
        'max_tier', 'smart_per_tier', 'total_players'
    )
    list_filter = ('is_active', 'start_date', 'end_date')
    search_fields = ('name', 'description')
    readonly_fields = ('created', 'last_update')
    actions = ['activate_season']

    fieldsets = (
        ('Season Info', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Dates', {
            'fields': ('start_date', 'end_date')
        }),
        ('Battle Pass Settings', {
            'fields': ('max_tier', 'smart_per_tier')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def is_active_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓ Active</span>')
        return format_html('<span style="color: red;">✗ Inactive</span>')
    is_active_display.short_description = 'Status'

    def total_players(self, obj):
        return obj.player_progress.count()
    total_players.short_description = 'Players'

    def activate_season(self, request, queryset):
        for season in queryset:
            season.activate()
        self.message_user(request, f"Activated {queryset.count()} season(s)")
    activate_season.short_description = "Activate selected seasons"


@admin.register(BattlePassTier)
class BattlePassTierAdmin(BaseAdmin):
    list_display = (
        'tier_number', 'season', 'smart_required', 'reward_count', 'created'
    )
    list_filter = ('season', 'tier_number')
    search_fields = ('season__name',)
    raw_id_fields = ('season',)
    readonly_fields = ('created', 'last_update')
    ordering = ('season', 'tier_number')

    fieldsets = (
        ('Tier Info', {
            'fields': ('season', 'tier_number', 'smart_required')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def reward_count(self, obj):
        return obj.rewards.count()
    reward_count.short_description = 'Rewards'


@admin.register(BattlePassReward)
class BattlePassRewardAdmin(BaseAdmin):
    list_display = (
        'name', 'tier_info', 'tier_type', 'reward_type',
        'coin_amount', 'claimed_count', 'created'
    )
    list_filter = ('tier_type', 'reward_type', 'tier__season')
    search_fields = ('name', 'description', 'tier__season__name')
    raw_id_fields = ('tier',)
    readonly_fields = ('created', 'last_update')

    fieldsets = (
        ('Reward Info', {
            'fields': ('tier', 'tier_type', 'name', 'description')
        }),
        ('Reward Details', {
            'fields': ('reward_type', 'coin_amount', 'item_id', 'image_url')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def tier_info(self, obj):
        return f"Tier {obj.tier.tier_number} - {obj.tier.season.name}"
    tier_info.short_description = 'Tier'

    def claimed_count(self, obj):
        return obj.claimed_by.count()
    claimed_count.short_description = 'Claims'


@admin.register(BattlePassProgress)
class BattlePassProgressAdmin(BaseAdmin):
    list_display = (
        'member', 'season', 'current_tier', 'progress_percentage',
        'member_season_smart', 'member_premium', 'last_update'
    )
    list_filter = ('season', 'current_tier', 'member__season_premium')
    search_fields = ('member__username', 'member__handle', 'season__name')
    raw_id_fields = ('member', 'season')
    readonly_fields = ('created', 'last_update')

    fieldsets = (
        ('Progress Info', {
            'fields': ('member', 'season', 'current_tier')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def progress_percentage(self, obj):
        if obj.season.max_tier == 0:
            return "0%"
        percentage = min((obj.current_tier / obj.season.max_tier) * 100, 100)
        return f"{percentage:.1f}%"
    progress_percentage.short_description = 'Progress'

    def member_season_smart(self, obj):
        return obj.member.season_smart
    member_season_smart.short_description = 'Season Smart'

    def member_premium(self, obj):
        if obj.member.season_premium:
            return format_html('<span style="color: gold;">★ Premium</span>')
        return format_html('<span style="color: gray;">Free</span>')
    member_premium.short_description = 'Premium'


@admin.register(ClaimedReward)
class ClaimedRewardAdmin(BaseAdmin):
    list_display = (
        'member', 'reward_name', 'tier_info', 'reward_type',
        'claimed_at', 'created'
    )
    list_filter = (
        'reward__tier_type', 'reward__reward_type',
        'reward__tier__season', 'claimed_at'
    )
    search_fields = (
        'member__username', 'member__handle',
        'reward__name', 'reward__tier__season__name'
    )
    raw_id_fields = ('member', 'reward')
    readonly_fields = ('claimed_at', 'created', 'last_update')
    date_hierarchy = 'claimed_at'

    fieldsets = (
        ('Claim Info', {
            'fields': ('member', 'reward', 'claimed_at')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

    def reward_name(self, obj):
        return obj.reward.name
    reward_name.short_description = 'Reward'

    def tier_info(self, obj):
        return f"Tier {obj.reward.tier.tier_number}"
    tier_info.short_description = 'Tier'

    def reward_type(self, obj):
        return obj.reward.get_reward_type_display()
    reward_type.short_description = 'Type'


@admin.register(ProphauntWeapon)
class ProphauntWeaponAdmin(BaseAdmin):
    list_display = (
        'id', 'name', 'price', 'damage', 'order', 'is_test', 'min_version'
    )
    list_editable = ('order', 'is_test', 'price', 'damage', 'min_version')
    list_filter = ('is_test', 'min_version')
    search_fields = ('name', 'resource_path')
    readonly_fields = ('created', 'last_update')
    ordering = ('order', 'name')

    fieldsets = (
        ('Weapon Info', {
            'fields': ('name', 'resource_path', 'order')
        }),
        ('Stats', {
            'fields': ('price', 'damage')
        }),
        ('Settings', {
            'fields': ('is_test', 'min_version')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )


@admin.register(ProphauntWeaponOwns)
class ProphauntWeaponOwnsAdmin(BaseAdmin):
    list_display = (
        'id', 'member', 'weapon', 'ammo', 'created'
    )
    list_filter = ('weapon', 'created')
    search_fields = ('member__username', 'member__handle', 'weapon__name')
    raw_id_fields = ('member', 'weapon')
    readonly_fields = ('created', 'last_update')

    fieldsets = (
        ('Ownership Info', {
            'fields': ('member', 'weapon', 'ammo')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )


@admin.register(ProphauntItemData)
class ProphauntItemDataAdmin(BaseAdmin):
    list_display = (
        'member', 'current_selected_weapon', 'grenades', 'hexes', 'last_update'
    )
    list_filter = ('current_selected_weapon', 'grenades', 'hexes')
    search_fields = ('member__username', 'member__handle')
    raw_id_fields = ('member', 'current_selected_weapon')
    readonly_fields = ('created', 'last_update')

    fieldsets = (
        ('Player Info', {
            'fields': ('member', 'current_selected_weapon')
        }),
        ('Items', {
            'fields': ('grenades', 'hexes')
        }),
        ('Metadata', {
            'fields': ('created', 'last_update'),
            'classes': ('collapse',)
        })
    )

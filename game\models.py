import json
from django.db import models
from django.utils import timezone
from utils.models import BaseModel

STATE_LOBBY = "Lobby"
STATE_COUNTDOWN = "CountDown"
STATE_INGAME = "InGame"
STATE_RESULTS = "Results"
STATE_LOADING = "Loading"

STATE_CHOICES = (
    (STATE_LOBBY, STATE_LOBBY),
    (STATE_COUNTDOWN, STATE_COUNTDOWN),
    (STATE_INGAME, STATE_INGAME),
    (STATE_RESULTS, STATE_RESULTS),
    (STATE_LOADING, STATE_LOADING),
)


MODE_RACE = "Race"
MODE_FREERIDE = "FreeRide"
MODE_PROPHAUNT = "PropHaunt"

MODE_CHOICES = (
    (MODE_RACE, MODE_RACE),
    (MODE_FREERIDE, MODE_FREERIDE),
    (MODE_PROPHAUNT, MODE_PROPHAUNT),
)

# Map type choices (subset of game modes for maps)
MAP_TYPE_RACE = "Race"
MAP_TYPE_PROPHAUNT = "PropHaunt"

MAP_TYPE_CHOICES = (
    (MAP_TYPE_RACE, MAP_TYPE_RACE),
    (MAP_TYPE_PROPHAUNT, MAP_TYPE_PROPHAUNT),
)


class GameServer(BaseModel):
    ip = models.CharField(max_length=100, default='')
    port = models.IntegerField(default=9000)
    state = models.CharField(max_length=30, choices=STATE_CHOICES, default=STATE_LOBBY)
    mode = models.CharField(max_length=30, choices=MODE_CHOICES, default=MODE_FREERIDE)
    players_joined = models.IntegerField(default=0)
    min_players_start = models.IntegerField(default=0)
    max_players_start = models.IntegerField(default=40)
    qualified_count = models.IntegerField(default=0)
    last_update = models.DateTimeField(default=timezone.now)
    is_alive = models.BooleanField(default=True)
    map = models.ForeignKey("game.Map", on_delete=models.CASCADE, null=True, blank=True)
    bot_count = models.IntegerField(default=0)
    is_test = models.BooleanField(default=False)
    current_stage = models.IntegerField(default=1)
    total_stage = models.IntegerField(default=1)
    hide = models.BooleanField(default=False)
    has_password = models.BooleanField(default=False)
    password = models.CharField(max_length=50, default='', blank=True)
    name = models.CharField(max_length=100, default='', blank=True)
    is_private = models.BooleanField(default=False)
    owner = models.ForeignKey("account.Member", related_name='owned_servers', on_delete=models.CASCADE, null=True, blank=True)
    due_date = models.DateTimeField(null=True, blank=True, default=timezone.now)
    owner_note = models.TextField(null=True, blank=True)
    force_new_player = models.BooleanField(default=True)
    force_reconnect = models.BooleanField(default=True)
    gun_allow = models.BooleanField(default=False)
    trusted = models.BooleanField(default=False)
    server_key = models.CharField(max_length=100, default='')
    max_vehicle = models.IntegerField(default=20)


    @property
    def owner_handle(self):
        if self.owner is None:
            return ""
        
        return self.owner.handle
    

    @property
    def remaining_time(self):
        delta = (timezone.now() - self.due_date)
        if delta.seconds <= 0:
            return 0
        
        return delta.seconds


    @property
    def real_players(self):
        return self.players_joined - self.bot_count


    @property
    def _map(self):
        if self.mode == MODE_FREERIDE:
            return "AzadShahr"
        return self.map


    def __str__(self):
        return self.ip + ":" + str(self.port)


DIFFICULTY_EASY = "EASY"
DIFFICULTY_MEDIUM = "MEDIUM"
DIFFICULTY_HARD = "HARD"

DIFFICULTY_CHOICES = (
    (DIFFICULTY_EASY, DIFFICULTY_EASY),
    (DIFFICULTY_MEDIUM, DIFFICULTY_MEDIUM),
    (DIFFICULTY_HARD, DIFFICULTY_HARD),
)


class Map(BaseModel):
    name = models.CharField(max_length=100, default='')
    path = models.CharField(max_length=200, default='')
    image_path = models.CharField(max_length=300, default='')
    difficulty = models.CharField(max_length=300, default=DIFFICULTY_EASY,choices=DIFFICULTY_CHOICES)
    type = models.CharField(max_length=30, choices=MAP_TYPE_CHOICES, default=MAP_TYPE_RACE, help_text="Map type for game mode compatibility")
    min_players_count = models.IntegerField(default=20)
    max_players_count = models.IntegerField(default=100)
    qualified_count = models.IntegerField(default=8)
    bot_count = models.IntegerField(default=0)
    bot_wait_remove = models.IntegerField(default=15) #Time waits after all players finishes
    bot_reset_time = models.IntegerField(default=200) #Time waits after repetetive bot reset
    weight = models.IntegerField(default=6)
    is_active = models.BooleanField(default=True)
    beta = models.BooleanField(default=False)
    is_test = models.BooleanField(default=False)


    def __str__(self):
        return self.name


class MapStage(BaseModel):
    stage = models.IntegerField(default=1)
    maps = models.ManyToManyField(Map, blank=True)


class GameSettings(BaseModel):
    lobby_time = models.IntegerField(default=0)
    result_time = models.IntegerField(default=10)
    countdown_time = models.IntegerField(default=10)
    game_time = models.IntegerField(default=0)
    player_join_add_time = models.IntegerField(default=0)
    coin_multiplier = models.IntegerField(default=1)
    reward_claim_interval = models.IntegerField(default=60)
    normal_total_stage = models.IntegerField(default=1)
    prophaunt_round_time = models.IntegerField(default=180)
    prophaunt_lobby_time = models.IntegerField(default=180)
    prophaunt_rounds_per_game = models.IntegerField(default=3)
    prophaunt_sound_cooldown = models.FloatField(default=4.0)
    prophaunt_stealth_period = models.FloatField(default=20.0)
    prophaunt_show_period = models.FloatField(default=8.0)
    prophaunt_hex_duration = models.FloatField(default=4.0)
    prophaunt_hex_cooldown = models.FloatField(default=30.0)
    prophaunt_grenade_damage = models.FloatField(default=75.0)
    prophaunt_grenade_radius = models.FloatField(default=5.0)
    prophaunt_gun_damage = models.FloatField(default=25.0)
    prophaunt_self_damage = models.FloatField(default=10.0)

class Game(BaseModel):
    game_server = models.ForeignKey("game.GameServer", related_name='games', on_delete=models.CASCADE)
    game_container = models.ForeignKey("game.GameContainer", related_name='games', on_delete=models.CASCADE, null=True, default=None)
    finished = models.DateTimeField(null=True, default=None, blank=True)
    is_finished = models.BooleanField(default=False)
    member = models.ManyToManyField("account.Member", through="game.GameJoined")
    stage = models.IntegerField(default=1)
    total_stage = models.IntegerField(default=1)


class GameContainer(BaseModel):
    game_server = models.ForeignKey("game.GameServer", related_name='containers', on_delete=models.CASCADE)


class GameJoined(BaseModel):
    game = models.ForeignKey("game.Game", related_name='players', on_delete=models.CASCADE)
    member = models.ForeignKey("account.Member", related_name='players', on_delete=models.CASCADE, null=True)
    time = models.IntegerField(default=-1)
    finished = models.BooleanField(default=False)
    handle = models.CharField(max_length=100, default="", blank=True)
    rank = models.IntegerField(default=-1)
    cup = models.IntegerField(default=0)
    coin = models.IntegerField(default=0)
    crown = models.IntegerField(default=0)
    character = models.ForeignKey("shop.Character", on_delete=models.CASCADE, null=True)
    map = models.ForeignKey("game.Map", on_delete=models.CASCADE, null=True)
    is_bot = models.BooleanField(default=False)
    bot_id = models.IntegerField(default=0)
    fps = models.IntegerField(default=-1)
    fps_count = models.IntegerField(default=0)
    ping = models.IntegerField(default=-1)
    ping_count = models.IntegerField(default=0)


    @property
    def game_server(self):
        return self.game.game_server


    @property
    def stage(self):
        return self.game.stage


    @property
    def total_stage(self):
        return self.game.total_stage

    @property
    def container_id(self):
        if self.game.game_container == None:
            return ""
        return self.game.game_container.id
    

    @property
    def _fps(self):
        if self.fps == -1 or self.fps_count == 0:
            return -1

        return "%.2f" % (float(self.fps) / float(self.fps_count))


    @property
    def _ping(self):
        if self.ping == -1 or self.ping_count == 0:
            return -1

        return "%.2f" % (float(self.ping) / float(self.ping_count))


class MessageReport(BaseModel):
    member = models.ForeignKey("account.Member", related_name='reports', on_delete=models.CASCADE, null=True)
    content = models.TextField(default="")
    message_create_time = models.DateTimeField(default=timezone.now)
    message_id = models.CharField(max_length=200, default="")
    sender_id = models.CharField(max_length=200, default="")
    nakama_username = models.CharField(max_length=200, default="")
    handled = models.BooleanField(default=False)

    @property
    def message_txt(self):
        return json.loads(self.content).get("message")


    @property
    def handle(self):
        return json.loads(self.content).get("handle")


    @property
    def username(self):
        return json.loads(self.content).get("id")


    @property
    def report_handle(self):
        return self.member.handle


class ContactUsMessage(BaseModel):
    member = models.ForeignKey("account.Member", related_name='messages', on_delete=models.CASCADE, null=True)
    message = models.TextField(default="")

    @property
    def handle(self):
        return self.member.handle


class RequestError(BaseModel):
    content = models.TextField(default="")


class PurchaseWithCoin(BaseModel):
    member = models.ForeignKey("account.Member", related_name='purchases', on_delete=models.CASCADE, null=True)
    price = models.IntegerField(default=0)
    type = models.CharField(max_length=100, default='')
    secondary = models.CharField(max_length=100, default='')
    forgiving = models.BooleanField(default=False)
    success = models.BooleanField(default=False)
    after_coin = models.IntegerField(default=0)


class AddCoin(BaseModel):
    member = models.ForeignKey("account.Member", related_name='addcoins', on_delete=models.CASCADE, null=True)
    value = models.IntegerField(default=0)
    type = models.CharField(max_length=100, default='')
    secondary = models.CharField(max_length=100, default='')
    after_coin = models.IntegerField(default=0)

    @property
    def handle(self):
        return self.member.handle


class OnlineStatistics(BaseModel):
    number_of_freeride_users = models.IntegerField(default=0)
    number_of_race_users = models.IntegerField(default=0)
    max_freeride_server_user = models.IntegerField(default=0)
    max_race_server_user = models.IntegerField(default=0)
    number_of_freeride_servers = models.IntegerField(default=0)
    number_of_race_servers = models.IntegerField(default=0)
    free_race_servers = models.IntegerField(default=0)
    total_online_players = models.IntegerField(default=0)


class CoinTransfer(BaseModel):
    from_member = models.ForeignKey("account.Member", related_name='transfer_froms', on_delete=models.CASCADE, null=True, blank=True)
    to_member = models.ForeignKey("account.Member", related_name='transfer_tos', on_delete=models.CASCADE, null=True, blank=True)
    amount = models.IntegerField(default=0)
    fee = models.IntegerField(default=0)
    from_coin_start = models.IntegerField(default=0)
    from_coin_end = models.IntegerField(default=0)
    to_coin_start = models.IntegerField(default=0)
    to_coin_end = models.IntegerField(default=0)
    success = models.BooleanField(default=False)

    @property
    def from_handle(self):
        if self.from_member == None:
            return str(self.from_member.id) + ""
        return str(self.from_member.id) + " " + self.from_member.handle


    @property
    def to_handle(self):
        if self.to_member == None:
            return ""
        return str(self.to_member.id) + " " + self.to_member.handle


    @property
    def from_handle_small(self):
        if self.from_member == None:
            return ""
        
        if self.from_member.hide_in_ranks:
            return "مخفی شده"
        return self.from_member.handle
    
    @property
    def to_username(self):
        if self.to_member == None:
            return ""
        
        return self.to_member.username


    @property
    def from_username(self):
        if self.from_member == None:
            return ""
        
        return self.from_member.username


    @property
    def to_handle_small(self):
        if self.to_member == None:
            return ""

        if self.to_member.hide_in_ranks:
            return "مخفی شده"
        return self.to_member.handle


class JobDone(BaseModel):
    member = models.ForeignKey("account.Member", related_name='jobdones', on_delete=models.CASCADE, null=True)
    title = models.CharField(max_length=100, default='')
    job = models.CharField(max_length=100, default='')

    @property
    def handle(self):
        return self.member.handle


class GiftCode(BaseModel):
    code = models.CharField(max_length=100, default='')
    gift_coin = models.IntegerField(default=0)
    gift_character = models.ForeignKey('shop.Character', null=True, blank=True, on_delete=models.CASCADE)
    count = models.IntegerField(default=1)
    redeem_count = models.IntegerField(default=0)

    def __str__(self):
        return self.code


class Redeem(BaseModel):
    member = models.ForeignKey("account.Member", related_name='redeems', on_delete=models.CASCADE)
    code = models.ForeignKey(GiftCode, on_delete=models.CASCADE)


class BazrasReport(BaseModel):
    bazras = models.ForeignKey("account.Member", related_name='bazras_reports', on_delete=models.CASCADE)
    type = models.CharField(max_length=100, blank=True, null=True)
    message = models.TextField(blank=True)
    member = models.ForeignKey("account.Member", related_name='bazras_member_reports', on_delete=models.CASCADE)
    time = models.IntegerField(default=0)

    @property
    def bazras_handle(self):
        if self.bazras is None:
            return ""
        
        return self.bazras.handle
    

    @property
    def member_handle(self):
        if self.member is None:
            return ""
        
        return self.member.handle
